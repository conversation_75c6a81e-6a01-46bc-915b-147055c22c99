import React, { useEffect, useState } from 'react';
import { Modal, View, StyleSheet, Image, TouchableOpacity, Text, Linking } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { setHasSeenWelcomePopup, getPopUps, setCurrentPopup, clearCurrentPopup } from '../redux/popupSlice';
import Icon from 'react-native-vector-icons/FontAwesome';

export const PassPopup = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const { popups, currentPopup, isLoading } = useSelector((state) => state.popup);
  const [showPopup, setShowPopup] = useState(false);
  const [displayTimer, setDisplayTimer] = useState(null);

  // Filter eligible popups based on criteria
  const getEligiblePopups = () => {
    return popups.filter(popup =>
      popup.is_active === true &&
      (popup.approval_status === 'approved_by_care' || popup.approval_status === 'approved_by_admin')
    );
  };

  // Select random popup from eligible ones
  const selectRandomPopup = () => {
    const eligiblePopups = getEligiblePopups();
    if (eligiblePopups.length > 0) {
      const randomIndex = Math.floor(Math.random() * eligiblePopups.length);
      return eligiblePopups[randomIndex];
    }
    return null;
  };

  // Fetch popups and set up display logic
  useEffect(() => {
    if (visible && popups.length === 0) {
      dispatch(getPopUps());
    }
  }, [visible, dispatch, popups.length]);

  // Handle popup display timing
  useEffect(() => {
    if (visible && popups.length > 0 && !currentPopup) {
      const selectedPopup = selectRandomPopup();
      if (selectedPopup) {
        dispatch(setCurrentPopup(selectedPopup));

        // Set delay before showing popup
        const delayTimer = setTimeout(() => {
          setShowPopup(true);

          // Set display duration timer
          const durationTimer = setTimeout(() => {
            handleClose();
          }, selectedPopup.display_duration);

          setDisplayTimer(durationTimer);
        }, selectedPopup.delay_ms);

        return () => {
          clearTimeout(delayTimer);
          if (displayTimer) {
            clearTimeout(displayTimer);
          }
        };
      }
    }
  }, [visible, popups, currentPopup, dispatch]);

  const handleClose = () => {
    setShowPopup(false);
    dispatch(clearCurrentPopup());
    dispatch(setHasSeenWelcomePopup(true));
    if (displayTimer) {
      clearTimeout(displayTimer);
      setDisplayTimer(null);
    }
    onClose();
  };

  const handleLinkPress = () => {
    if (currentPopup?.link_url) {
      Linking.openURL(currentPopup.link_url);
    }
  };

  // Don't render if no popup is selected or not ready to show
  if (!currentPopup || !showPopup) {
    return null;
  }

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible && showPopup}
      onRequestClose={handleClose}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        onPress={handleClose}
        activeOpacity={1}
      >
        <View style={styles.modalContent}>
          {/* Cross Icon */}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            activeOpacity={0.7}
          >
            <Icon name="times" size={24} color="#fff" />
          </TouchableOpacity>

          {/* Content based on content_type */}
          {currentPopup.content_type === 'text_only' && (
            <View style={styles.textContainer}>
              <Text style={styles.titleText}>{currentPopup.title}</Text>
              <Text style={styles.contentText}>{currentPopup.text_content}</Text>
            </View>
          )}

          {currentPopup.content_type === 'image_only' && currentPopup.image && (
            <TouchableOpacity
              style={styles.imageContainer}
              onPress={handleClose}
              activeOpacity={1}
            >
              <Image
                source={{ uri: currentPopup.image }}
                style={styles.image}
                resizeMode="contain"
              />
            </TouchableOpacity>
          )}

          {currentPopup.content_type === 'text_image' && (
            <>
              <View style={styles.textContainer}>
                <Text style={styles.titleText}>{currentPopup.title}</Text>
                <Text style={styles.contentText}>{currentPopup.text_content}</Text>
              </View>
              {currentPopup.image && (
                <TouchableOpacity
                  style={styles.imageContainer}
                  onPress={handleClose}
                  activeOpacity={1}
                >
                  <Image
                    source={{ uri: currentPopup.image }}
                    style={styles.image}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              )}
            </>
          )}

          {currentPopup.content_type === 'text_link' && (
            <View style={styles.textContainer}>
              <Text style={styles.titleText}>{currentPopup.title}</Text>
              <Text style={styles.contentText}>{currentPopup.text_content}</Text>
              {currentPopup.link_url && (
                <TouchableOpacity onPress={handleLinkPress} style={styles.linkButton}>
                  <Text style={styles.linkText}>{currentPopup.link_text || 'Click here'}</Text>
                </TouchableOpacity>
              )}
            </View>
          )}

          {currentPopup.content_type === 'link_anchor' && (
            <View style={styles.textContainer}>
              <Text style={styles.titleText}>{currentPopup.title}</Text>
              <Text style={styles.contentText}>{currentPopup.description}</Text>
              {currentPopup.link_url && (
                <TouchableOpacity onPress={handleLinkPress} style={styles.linkButton}>
                  <Text style={styles.linkText}>{currentPopup.link_text || 'Click here'}</Text>
                </TouchableOpacity>
              )}
            </View>
          )}

          {/* Fallback: Show default image only if no content type matches or as fallback */}
          {!['text_only', 'image_only', 'text_image', 'text_link', 'link_anchor'].includes(currentPopup.content_type) && (
            <TouchableOpacity
              style={styles.imageContainer}
              onPress={handleClose}
              activeOpacity={1}
            >
              <Image
                source={require('../../assets/pass.png')}
                style={styles.image}
                resizeMode="contain"
              />
            </TouchableOpacity>
          )}

          {/* Instruction Text */}
          <Text style={styles.instructionText}>
            Tap anywhere or press the cross button to hide this
          </Text>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: 'transparent',
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
    borderRadius: 10,
    marginBottom: 10,
  },
  titleText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  contentText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 10,
  },
  linkButton: {
    backgroundColor: '#28a745',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    alignSelf: 'center',
    marginTop: 10,
  },
  linkText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  imageContainer: {
    width: '100%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  instructionText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 20,
    opacity: 0.8,
  }
});
